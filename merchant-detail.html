<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户详情</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .back-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 14px;
            padding: 8px 16px;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: var(--primary-blue-light);
        }

        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }
        .card-title1 {
            font-size: 20px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        /* 纵向Tab布局 */
        .tabs-container {
            display: flex;
            gap: 24px;
            align-items: flex-start;
        }

        .tabs-nav {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 200px;
        }

        .tab {
            padding: 16px 20px;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            background: var(--bg-container);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
        }

        .tab.active {
            color: var(--primary-blue);
            background: var(--primary-blue-light);
            border-color: var(--primary-blue);
        }

        .tab:hover {
            color: var(--primary-blue);
            background: var(--primary-blue-light);
        }

        .tab-content-area {
            flex: 1;
        }

        .tab-content {
            display: none;
            background: var(--bg-container);
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
        }

        .tab-content.active {
            display: block;
        }

        .tab-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .tab-actions {
            display: flex;
            gap: 12px;
        }

        /* 模态弹框样式 */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            max-width: 95%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: var(--text-primary);
            font-size: 18px;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-secondary);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-control {
            width: 100%;
            max-width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        }

        .form-control[readonly] {
            background-color: #f5f5f5;
            color: var(--text-secondary);
        }

        .form-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        select.form-control[multiple] {
            height: 120px;
        }

        .checkbox-group {
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 12px;
            max-height: 200px;
            overflow-y: auto;
            background: white;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            cursor: pointer;
            font-weight: normal;
        }

        .checkbox-item:last-child {
            margin-bottom: 0;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        .checkbox-item span {
            font-size: 14px;
            color: var(--text-primary);
        }

        .checkbox-item:hover {
            background-color: #f5f5f5;
            border-radius: 4px;
            padding: 4px;
            margin: 2px 0;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            background: white;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            margin: 0;
        }

        .table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            white-space: nowrap;
        }

        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table tbody tr:hover {
            background-color: #f5f5f5;
        }

        /* 多选下拉组件样式 */
        .multi-select-container {
            position: relative;
        }

        .multi-select-input {
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px 12px;
            background: white;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 20px;
        }

        .multi-select-input:hover {
            border-color: var(--primary-blue);
        }

        .selected-items {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .selected-item {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .selected-item .remove {
            cursor: pointer;
            font-weight: bold;
        }

        .placeholder {
            color: var(--text-disabled);
            font-size: 14px;
        }

        .dropdown-arrow {
            color: var(--text-secondary);
            transition: transform 0.3s;
        }

        .dropdown-arrow.open {
            transform: rotate(180deg);
        }

        .multi-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-color);
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .multi-select-dropdown.open {
            display: block;
        }

        .dropdown-option {
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .dropdown-option:hover {
            background-color: #f5f5f5;
        }

        .dropdown-option input[type="checkbox"] {
            margin: 0;
        }

        .dropdown-option label {
            margin: 0;
            cursor: pointer;
            flex: 1;
        }

        .tab-body {
            padding: 24px;
        }

        /* 360度风险画像样式 */
        .risk-radar {
            position: relative;
            width: 100%;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .risk-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .merchant-avatar {
            width: 80px;
            height: 80px;
            background: var(--primary-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
        }

        .merchant-avatar i {
            font-size: 32px;
            color: white;
        }

        .merchant-info {
            background: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .merchant-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .merchant-id {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .risk-level {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 风险指标卡片 */
        .risk-card {
            position: absolute;
            width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color-light);
            transition: all 0.3s ease;
        }

        .risk-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .risk-card-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-card-header i {
            font-size: 16px;
            color: var(--primary-blue);
        }

        .risk-card-header span:nth-child(2) {
            flex: 1;
            font-weight: 500;
            color: var(--text-primary);
        }

        .risk-card-content {
            padding: 16px;
        }

        .risk-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 1.4;
        }

        .risk-item:last-child {
            margin-bottom: 0;
        }

        .risk-label {
            color: var(--text-secondary);
            min-width: 70px;
            margin-right: 8px;
        }

        .risk-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        /* 风险卡片位置 */
        .risk-card-1 { top: 10%; left: 10%; }
        .risk-card-2 { top: 10%; right: 10%; }
        .risk-card-3 { bottom: 10%; left: 10%; }
        .risk-card-4 { bottom: 10%; right: 10%; }

        /* 低风险指标底部排列 */
        .low-risk-section {
            margin-top: 40px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }

        .low-risk-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .low-risk-card {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .info-label {
            font-weight: 500;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .info-value {
            color: var(--text-primary);
            font-size: 14px;
            word-break: break-all;
        }

        .section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color-light);
        }

        .photo-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }

        .photo-item {
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .photo-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .photo-label {
            padding: 8px 12px;
            font-size: 12px;
            color: var(--text-secondary);
            text-align: center;
            background: var(--bg-container);
        }

        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }
        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: #ffffff;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-warning {
            background: var(--warning-orange);
            border-color: var(--warning-orange);
            color: #ffffff;
        }

        .btn-warning:hover {
            background: #d46b08;
            border-color: #d46b08;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            padding: 24px;
            border-top: 1px solid var(--border-color-light);
            background: #fafafa;
        }

        .risk-indicators {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }

        .risk-category {
            background: var(--bg-container);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
        }

        .risk-category-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color-light);
        }

        .risk-item:last-child {
            border-bottom: none;
        }

        .risk-name {
            color: var(--text-primary);
            font-size: 14px;
        }

        .risk-level {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .risk-high {
            background: #fff2f0;
            color: var(--error-red);
        }

        .risk-medium {
            background: #fff7e6;
            color: var(--warning-orange);
        }

        .risk-low {
            background: #f6ffed;
            color: var(--success-green);
        }

        .operation-history {
            margin-top: 32px;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .history-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
        }

        .history-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
        }

        .status-tag {
            padding: 4px 12px;
            
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }
        .status-tag1 {
            padding: 4px 12px;
            margin-right:4px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }
        
        .status-pending {
            background: #f5f5f5;
            color: #8c8c8c;
        }

        .status-completed {
            background: #f6ffed;
            color: var(--success-green);
        }

        .status-processing {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面标题 -->
        

        <!-- 纵向Tab布局 -->
        <div class="tabs-container">
            <!-- Tab导航 -->
            <div class="tabs-nav">
                <div class="tab active" onclick="switchTab('audit')">
                    <i class="fas fa-clipboard-check"></i>
                    商户详情
                </div>
                <div class="tab" onclick="switchTab('risk')">
                    <i class="fas fa-exclamation-triangle"></i>
                    风险评估
                </div>
                <div class="tab" onclick="switchTab('history')">
                    <i class="fas fa-history"></i>
                    操作历史
                </div>
            </div>

            <!-- Tab内容区域 -->
            <div class="tab-content-area">

                <!-- 审查详情Tab -->
                <div id="audit-tab" class="tab-content active">
                    <div class="tab-header">
                        <h3 class="card-title1">商户详情</h3>
                        <div class="tab-actions">
                            <button class="btn btn-success" onclick="markCompleted()">
                                <i class="fas fa-check"></i>
                                标记完成
                            </button>
                            <button class="btn btn-warning" onclick="createRectificationTask()">
                                <i class="fas fa-tasks"></i>
                                下发整改任务
                            </button>
                        </div>
                    </div>
                    <div class="tab-body">
                        <!-- 基础信息 -->
                        <div class="section">
                    <h3 class="section-title">审查信息</h3>
                    <div class="info-grid">
                        
                        
                        <div class="info-item">
                            <div class="info-label">审查时间</div>
                            <div class="info-value">2024-01-16 09:15:30</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">审查状态</div>
                            <div class="info-value"><span class="status-tag status-pending">待审查</span></div>
                        </div>
                        
                    </div>
                </div>

                <!-- 商户信息 -->
                <div class="section">
                    <h3 class="section-title">商户基本信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">商户号</div>
                            <div class="info-value">M202401001</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">商户名称</div>
                            <div class="info-value">北京张三便利店有限公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">商户简称</div>
                            <div class="info-value">张三便利店</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">商户类型</div>
                            <div class="info-value">个体工商户</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">经营范围</div>
                            <div class="info-value">日用百货、食品销售</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">商户地址</div>
                            <div class="info-value">北京市朝阳区朝阳路123号</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">进件时间</div>
                            <div class="info-value">2024-01-15 14:30:25</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">商户标签</div>
                            <div class="info-value"><span class="status-tag1 status-pending">固定场所</span><span class="status-tag1 status-pending">批发市场</span><span class="status-tag status-pending">水果</span></div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3 class="section-title">负责人信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">负责人</div>
                            <div class="info-value">张三</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">身份证号</div>
                            <div class="info-value">110101199001011234</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">手机号</div>
                            <div class="info-value">13800138001</div>
                        </div>
                        
                    </div>
                </div>
                <!-- 机构信息 -->
                <div class="section">
                    <h3 class="section-title">机构信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">市级分公司</div>
                            <div class="info-value">北京分公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">区县分公司</div>
                            <div class="info-value">朝阳区分公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">营业所</div>
                            <div class="info-value">朝阳路营业所</div>
                        </div>
                        
                    </div>
                </div>

                <!-- 店面照片 -->
                <div class="section">
                    <h3 class="section-title">店面照片</h3>
                    <div class="photo-grid">
                        <!-- 门头照片展位图 -->
                        <div class="photo-item">
                            <!-- SVG展位图：门头照片 -->
                            <svg width="120" height="120" viewBox="0 0 120 80" fill="none" xmlns="http://www.w3.org/2000/svg" style="background:#f3f4f6;border-radius:8px;">
                                <rect width="120" height="80" rx="8" fill="#f3f4f6"/>
                                <rect x="20" y="40" width="80" height="24" rx="4" fill="#e5e7eb"/>
                                <rect x="30" y="20" width="60" height="20" rx="3" fill="#d1d5db"/>
                                <text x="60" y="60" text-anchor="middle" fill="#9ca3af" font-size="12" font-family="Arial" dy="0.35em">门头照片</text>
                            </svg>
                            <div class="photo-label">门头照片</div>
                        </div>
                        <!-- 收银台场景照展位图 -->
                        <div class="photo-item">
                            <!-- SVG展位图：收银台场景照 -->
                            <svg width="120" height="120" viewBox="0 0 120 80" fill="none" xmlns="http://www.w3.org/2000/svg" style="background:#f3f4f6;border-radius:8px;">
                                <rect width="120" height="80" rx="8" fill="#f3f4f6"/>
                                <rect x="15" y="50" width="90" height="18" rx="4" fill="#e5e7eb"/>
                                <rect x="35" y="30" width="50" height="16" rx="3" fill="#d1d5db"/>
                                <circle cx="60" cy="40" r="8" fill="#e5e7eb"/>
                                <text x="60" y="65" text-anchor="middle" fill="#9ca3af" font-size="12" font-family="Arial" dy="0.35em">收银台场景照</text>
                            </svg>
                            <div class="photo-label">收银台场景照</div>
                        </div>
                        <!-- 店内场景照展位图 -->
                        <div class="photo-item">
                            <!-- SVG展位图：店内场景照 -->
                            <svg width="120" height="120" viewBox="0 0 120 80" fill="none" xmlns="http://www.w3.org/2000/svg" style="background:#f3f4f6;border-radius:8px;">
                                <rect width="120" height="80" rx="8" fill="#f3f4f6"/>
                                <rect x="18" y="30" width="84" height="30" rx="6" fill="#e5e7eb"/>
                                <rect x="30" y="20" width="20" height="10" rx="2" fill="#d1d5db"/>
                                <rect x="70" y="20" width="20" height="10" rx="2" fill="#d1d5db"/>
                                <text x="60" y="60" text-anchor="middle" fill="#9ca3af" font-size="12" font-family="Arial" dy="0.35em">店内场景照</text>
                            </svg>
                            <div class="photo-label">店内场景照</div>
                        </div>
                    </div>
                </div>

                <!-- 证件资料 -->
                

                <div class="section">
                    <h3 class="section-title">营业执照信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">营业执照正面</div>
                            <div class="info-value">图片占位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">营业执照号</div>
                            <div class="info-value">91110105MA01234567</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">营业地址</div>
                            <div class="info-value">北京市朝阳区朝阳路123号</div>
                        </div>
                       
                        <div class="info-item">
                            <div class="info-label">是否长期执照</div>
                            <div class="info-value">是</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">起始时间</div>
                            <div class="info-value">2023-06-15</div>
                        </div>
                        
                    </div>
                </div>
                <!-- 法人信息 -->
                <div class="section">
                    <h3 class="section-title">法人信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">身份证正面</div>
                            <div class="info-value">图片占位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">身份证国徽面</div>
                            <div class="info-value">图片占位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">姓名</div>
                            <div class="info-value">灵心</div>
                        </div>
                       
                        <div class="info-item">
                            <div class="info-label">手机号</div>
                            <div class="info-value">13800138001</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">证件类型</div>
                            <div class="info-value">身份证</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">证件号码</div>
                            <div class="info-value">110101199001011234</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">法人地址</div>
                            <div class="info-value">北京市朝阳区朝阳路123号</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">证件类型</div>
                            <div class="info-value">身份证</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">是否长期证件</div>
                            <div class="info-value">否</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">证件起始时间</div>
                            <div class="info-value">2023-06-15</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">证件结束时间</div>
                            <div class="info-value">2043-06-15</div>
                        </div>
                    </div>
                </div>
                <div class="section"> 
                <!--结算人信息-->
                <div class="section">
                    <h3 class="section-title">结算人信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">身份证正面</div>
                            <div class="info-value">图片占位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">身份证国徽面</div>
                            <div class="info-value">图片占位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">手持身份证</div>
                            <div class="info-value">图片占位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">姓名</div>
                            <div class="info-value">灵心</div>
                        </div>
                       
                        <div class="info-item">
                            <div class="info-label">手机号</div>
                            <div class="info-value">13800138001</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">证件类型</div>
                            <div class="info-value">身份证</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">证件号码</div>
                            <div class="info-value">110101199001011234</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">证件类型</div>
                            <div class="info-value">身份证</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">是否长期证件</div>
                            <div class="info-value">否</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">证件起始时间</div>
                            <div class="info-value">2023-06-15</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">证件结束时间</div>
                            <div class="info-value">2043-06-15</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3 class="section-title">结算银行信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">银行卡正面</div>
                            <div class="info-value">图片占位</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">结算银行</div>
                            <div class="info-value">中国邮政储蓄银行朝阳支行</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">结算联行号</div>
                            <div class="info-value">403100000007</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">银行卡号</div>
                            <div class="info-value">6222021234567890123</div>
                        </div>
                        
                        
                    </div>
                    <div class="section">
                        <h3 class="section-title">非法人授权函</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">附件</div>
                                <div class="info-value" style="display: flex; align-items: center; gap: 16px;">
                                    <!-- 真实PDF文件样式预览，带PDF图标和文件名 -->
                                    <a href="https://file-examples.com/storage/fe7b6e2e2e2e2e2e2e2e2e2e2e2e2e2e/sample.pdf" target="_blank" class="pdf-preview-link" style="display: flex; align-items: center; text-decoration: none; margin-right: 12px;">
                                        <!-- SVG PDF图标 -->
                                        <svg width="28" height="36" viewBox="0 0 28 36" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin-right:8px;">
                                            <rect x="2" y="2" width="24" height="32" rx="4" fill="#F2F2F2" stroke="#E53E3E" stroke-width="2"/>
                                            <rect x="2" y="2" width="24" height="8" rx="2" fill="#E53E3E"/>
                                            <text x="14" y="9" text-anchor="middle" fill="#fff" font-size="10" font-family="Arial" font-weight="bold">PDF</text>
                                        </svg>
                                        <span style="color: var(--text-primary); font-size: 14px; font-weight: 500;">非法人授权函.pdf</span>
                                    </a>
                                    <span style="color: var(--text-secondary); font-size: 12px;">PDF格式，支持在线预览与下载</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 风险详情Tab -->
                <div id="risk-tab" class="tab-content">
                    <div class="tab-header">
                        <h3 class="card-title1">风险评估</h3>
                        <button class="btn btn-primary" onclick="createVisitTask()">
                            <i class="fas fa-map-marker-alt"></i>
                            下发走访任务
                        </button>
                    </div>
                    <div class="tab-body">
                        <!-- 360度风险画像 -->
                        <div class="risk-radar">
                            <!-- 中心商户图标 -->
                            <div class="risk-center">
                                <div class="merchant-avatar">
                                    <i class="fas fa-store"></i>
                                </div>
                                <div class="merchant-info">
                                    <div class="merchant-name">张三便利店</div>
                                    <div class="merchant-id">M202401001</div>
                                    <div class="risk-level risk-high">高风险</div>
                                </div>
                            </div>

                            <!-- 风险指标卡片 -->
                            <div class="risk-card risk-card-1">
                                <div class="risk-card-header">
                                    <i class="fas fa-redo-alt"></i>
                                    <span>进件次数</span>
                                    <span class="risk-tag risk-high">高风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>小微商户5次及以上入驻次数</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>该商户历史进件7次</span>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-card risk-card-2">
                                <div class="risk-card-header">
                                    <i class="fas fa-camera"></i>
                                    <span>门头照重复</span>
                                    <span class="risk-tag risk-medium">中风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>与存量商户照片重复2-4次</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>门头照重复使用3次</span>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-card risk-card-3">
                                <div class="risk-card-header">
                                    <i class="fas fa-cash-register"></i>
                                    <span>收银台照重复</span>
                                    <span class="risk-tag risk-medium">中风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>与存量商户照片重复2-4次</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>收银台照重复使用2次</span>
                                    </div>
                                </div>
                            </div>

                            <div class="risk-card risk-card-4">
                                <div class="risk-card-header">
                                    <i class="fas fa-building"></i>
                                    <span>场景照重复</span>
                                    <span class="risk-tag risk-medium"></span>中风险</span>
                                </div>
                                <div class="risk-card-content">
                                    <div class="risk-item">
                                        <span class="risk-label">判定标准：</span>
                                        <span>与存量商户照片重复1次</span>
                                    </div>
                                    <div class="risk-item">
                                        <span class="risk-label">当前情况：</span>
                                        <span>场景照重复使用1次</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 低风险指标底部排列 -->
                        <div class="low-risk-section">
                            <h4 style="margin: 0 0 16px 0; color: var(--text-primary);">其他风险指标</h4>
                            <div class="low-risk-grid">
                                <div class="low-risk-card">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <i class="fas fa-id-card" style="color: var(--success-green);"></i>
                                        <span style="font-weight: 500;">身份证重复</span>
                                        <span class="risk-tag risk-low">低风险</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        与存量商户身份证重复0次
                                    </div>
                                </div>
                                <div class="low-risk-card">
                                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                        <i class="fas fa-file-alt" style="color: var(--success-green);"></i>
                                        <span style="font-weight: 500;">营业执照重复</span>
                                        <span class="risk-tag risk-low">低风险</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        与存量商户营业执照重复0次
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作历史Tab -->
                <div id="history-tab" class="tab-content">
                    <div class="tab-header">
                        <h3 class="card-title1">操作历史</h3>
                    </div>
                    <div class="tab-body">
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>操作时间</th>
                                        <th>操作人</th>
                                        <th>操作类型</th>
                                        <th>任务类型</th>
                                        <th>备注</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-01-15 14:30:00</td>
                                        <td>张经理</td>
                                        <td>审查</td>
                                        <td>-</td>
                                        <td>初次审查，发现高风险</td>
                                        <td><span class="status-tag status-completed">已完成</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-15 15:00:00</td>
                                        <td>张经理</td>
                                        <td>整改</td>
                                        <td>资料补充</td>
                                        <td>要求补充门头照片</td>
                                        <td><span class="status-tag status-processing">进行中</span></td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-14 16:20:00</td>
                                        <td>系统</td>
                                        <td>标记</td>
                                        <td>-</td>
                                        <td>新商户进件申请</td>
                                        <td><span class="status-tag status-completed">已完成</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    

                </div>
            </div>
        </div>
    </div>

    <!-- 标记完成弹框 -->
    <div id="markCompletedModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>标记审查完成</h3>
                <button class="modal-close" onclick="closeMarkCompletedModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>审查状态</label>
                    <input type="text" value="已完成" readonly class="form-control">
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <textarea id="completedRemark" class="form-control" rows="3" placeholder="请输入备注信息..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeMarkCompletedModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmMarkCompleted()">确认</button>
            </div>
        </div>
    </div>

    <!-- 下发整改任务弹框 -->
    <div id="rectificationModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3>下发整改任务</h3>
                <button class="modal-close" onclick="closeRectificationModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>任务类型</label>
                    <div class="multi-select-container">
                        <div class="multi-select-input" id="taskTypesInput" onclick="toggleTaskTypeDropdown()">
                            <div class="selected-items" id="selectedTaskTypes">
                                <span class="placeholder">请选择任务类型</span>
                            </div>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="multi-select-dropdown" id="taskTypesDropdown">
                            <div class="dropdown-option" data-value="基础资料补充">
                                <input type="checkbox" id="task1">
                                <label for="task1">基础资料补充</label>
                            </div>
                            <div class="dropdown-option" data-value="商户标签资料">
                                <input type="checkbox" id="task2">
                                <label for="task2">商户标签资料</label>
                            </div>
                            <div class="dropdown-option" data-value="法人身份证补充">
                                <input type="checkbox" id="task3">
                                <label for="task3">法人身份证补充</label>
                            </div>
                            <div class="dropdown-option" data-value="结算人身份证补充">
                                <input type="checkbox" id="task4">
                                <label for="task4">结算人身份证补充</label>
                            </div>
                            <div class="dropdown-option" data-value="营业执照补充">
                                <input type="checkbox" id="task5">
                                <label for="task5">营业执照补充</label>
                            </div>
                            <div class="dropdown-option" data-value="三证补充">
                                <input type="checkbox" id="task6">
                                <label for="task6">三证补充</label>
                            </div>
                            <div class="dropdown-option" data-value="银行卡补充">
                                <input type="checkbox" id="task7">
                                <label for="task7">银行卡补充</label>
                            </div>
                            <div class="dropdown-option" data-value="非法人授权书补充">
                                <input type="checkbox" id="task8">
                                <label for="task8">非法人授权书补充</label>
                            </div>
                        </div>
                    </div>
                    <small class="form-text">可选择多个任务类型</small>
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <textarea id="rectificationRemark" class="form-control" rows="3" placeholder="请输入备注信息..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeRectificationModal()">取消</button>
                <button class="btn btn-warning" onclick="confirmRectificationTask()">确认下发</button>
            </div>
        </div>


    </div>

    <script>
        // Tab切换功能
        function switchTab(tabName) {
            console.log('Switching to tab:', tabName); // 调试日志

            // 移除所有active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 添加active类到当前tab按钮
            document.querySelectorAll('.tab').forEach(tab => {
                const onclick = tab.getAttribute('onclick');
                if (onclick && onclick.includes(`'${tabName}'`)) {
                    tab.classList.add('active');
                }
            });

            // 显示对应的tab内容
            const targetTab = document.getElementById(tabName + '-tab');
            console.log('Target tab element:', targetTab); // 调试日志
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('Tab activated successfully'); // 调试日志
            } else {
                console.error('Tab not found:', tabName + '-tab'); // 错误日志
            }
        }

        // 创建整改任务
        function createRectificationTask() {
            if (confirm('确定要为该商户下发整改任务吗？')) {
                alert('整改任务已下发，任务编号：RT202401001');
                // 这里可以调用API创建整改任务
            }
        }

        // 创建走访任务
        function createVisitTask() {
            if (confirm('确定要为该商户下发走访任务吗？')) {
                alert('走访任务已下发，任务编号：VT202401001');
                // 这里可以调用API创建走访任务
            }
        }

        // 添加风险卡片悬停效果
        document.addEventListener('DOMContentLoaded', function() {
            const riskCards = document.querySelectorAll('.risk-card');
            riskCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
        
        // 创建走访任务
        function createVisitTask() {
            if (confirm('确定要为该商户下发走访任务吗？')) {
                alert('走访任务已下发，任务编号：VT202401001');
                // 这里可以调用API创建走访任务
            }
        }
        
        // 获取URL参数
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }
        
        // 页面加载时获取商户ID
        document.addEventListener('DOMContentLoaded', function() {
            const merchantId = getUrlParameter('id');
            if (merchantId) {
                document.querySelector('.page-title').textContent = `商户详情 - ${merchantId}`;
                // 这里可以根据商户ID加载具体数据
            }

            // 弹框背景点击关闭
            const modals = ['markCompletedModal', 'rectificationModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            modal.style.display = 'none';
                        }
                    });
                }
            });

            // ESC键关闭弹框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    modals.forEach(modalId => {
                        const modal = document.getElementById(modalId);
                        if (modal && modal.style.display === 'block') {
                            modal.style.display = 'none';
                        }
                    });
                }
            });
        });

        // 标记完成功能
        function markCompleted() {
            document.getElementById('markCompletedModal').style.display = 'block';
        }

        function closeMarkCompletedModal() {
            document.getElementById('markCompletedModal').style.display = 'none';
            document.getElementById('completedRemark').value = '';
        }

        function confirmMarkCompleted() {
            const remark = document.getElementById('completedRemark').value;

            // 更新审查状态
            const statusElement = document.querySelector('.info-item .info-value .status-tag');
            if (statusElement) {
                statusElement.className = 'status-tag status-completed';
                statusElement.textContent = '已完成';
            }

            // 添加操作历史记录
            addOperationHistory('标记完成', '审查完成', remark || '无');

            closeMarkCompletedModal();
            alert('审查状态已更新为"已完成"');
        }

        // 下发整改任务功能 - 重写原有函数
        function createRectificationTask() {
            document.getElementById('rectificationModal').style.display = 'block';
        }

        function closeRectificationModal() {
            document.getElementById('rectificationModal').style.display = 'none';
            // 清空任务类型选择
            clearTaskTypeSelection();
            document.getElementById('rectificationRemark').value = '';
        }

        // 多选组件相关函数
        function toggleTaskTypeDropdown() {
            const dropdown = document.getElementById('taskTypesDropdown');
            const arrow = document.querySelector('.dropdown-arrow');

            if (dropdown.classList.contains('open')) {
                dropdown.classList.remove('open');
                arrow.classList.remove('open');
            } else {
                dropdown.classList.add('open');
                arrow.classList.add('open');
            }
        }

        function clearTaskTypeSelection() {
            // 清空所有复选框
            document.querySelectorAll('#taskTypesDropdown input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            // 更新显示
            updateSelectedTaskTypes();
            // 关闭下拉框
            document.getElementById('taskTypesDropdown').classList.remove('open');
            document.querySelector('.dropdown-arrow').classList.remove('open');
        }

        function updateSelectedTaskTypes() {
            const selectedContainer = document.getElementById('selectedTaskTypes');
            const checkedBoxes = document.querySelectorAll('#taskTypesDropdown input[type="checkbox"]:checked');

            selectedContainer.innerHTML = '';

            if (checkedBoxes.length === 0) {
                selectedContainer.innerHTML = '<span class="placeholder">请选择任务类型</span>';
            } else {
                checkedBoxes.forEach(checkbox => {
                    const option = checkbox.closest('.dropdown-option');
                    const value = option.getAttribute('data-value');

                    const selectedItem = document.createElement('span');
                    selectedItem.className = 'selected-item';
                    selectedItem.innerHTML = `
                        ${value}
                        <span class="remove" onclick="removeTaskType('${value}')">&times;</span>
                    `;
                    selectedContainer.appendChild(selectedItem);
                });
            }
        }

        function removeTaskType(value) {
            const option = document.querySelector(`#taskTypesDropdown .dropdown-option[data-value="${value}"]`);
            if (option) {
                const checkbox = option.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
                updateSelectedTaskTypes();
            }
        }

        // 初始化多选组件事件
        document.addEventListener('DOMContentLoaded', function() {
            // 为复选框添加事件监听
            document.querySelectorAll('#taskTypesDropdown input[type="checkbox"]').forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedTaskTypes);
            });

            // 点击外部关闭下拉框
            document.addEventListener('click', function(e) {
                const container = document.querySelector('.multi-select-container');
                if (container && !container.contains(e.target)) {
                    document.getElementById('taskTypesDropdown').classList.remove('open');
                    document.querySelector('.dropdown-arrow').classList.remove('open');
                }
            });
        });

        function confirmRectificationTask() {
            const checkedBoxes = document.querySelectorAll('#taskTypesDropdown input[type="checkbox"]:checked');
            const taskTypes = Array.from(checkedBoxes).map(checkbox => {
                return checkbox.closest('.dropdown-option').getAttribute('data-value');
            });
            const remark = document.getElementById('rectificationRemark').value;

            if (taskTypes.length === 0) {
                alert('请选择至少一个任务类型');
                return;
            }

            // 生成任务编号
            const taskId = 'RT' + new Date().getFullYear() +
                          String(new Date().getMonth() + 1).padStart(2, '0') +
                          String(new Date().getDate()).padStart(2, '0') +
                          String(Math.floor(Math.random() * 1000)).padStart(3, '0');

            // 添加操作历史记录
            addOperationHistory('下发整改任务', '整改任务',
                `任务类型：${taskTypes.join('、')}${remark ? '；备注：' + remark : ''}`);

            closeRectificationModal();
            // alert(`整改任务已下发，任务编号：${taskId}`);
        }

        // 添加操作历史记录
        function addOperationHistory(operationType, taskType, remark) {
            const historyTable = document.querySelector('#history-tab .table tbody');
            if (historyTable) {
                const now = new Date();
                const timeString = now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0') + ':' +
                    String(now.getSeconds()).padStart(2, '0');

                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td>${timeString}</td>
                    <td>审查员001</td>
                    <td>${operationType}</td>
                    <td>${taskType}</td>
                    <td>${remark}</td>
                    <td><span class="status-tag status-completed">已完成</span></td>
                `;
                historyTable.insertBefore(newRow, historyTable.firstChild);
            }
        }
    </script>
</body>
</html>