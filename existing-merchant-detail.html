<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存量商户风险详情</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 14px;
            padding: 8px 16px;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: var(--primary-blue-light);
        }

        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .info-value {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .risk-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .risk-high {
            background: #fff2f0;
            color: var(--error-red);
        }

        .risk-medium {
            background: #fff7e6;
            color: var(--warning-orange);
        }

        .risk-low {
            background: #f6ffed;
            color: var(--success-green);
        }

        .risk-indicators {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }

        .indicator-card {
            background: #fafafa;
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
        }

        .indicator-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .indicator-table {
            width: 100%;
            border-collapse: collapse;
        }

        .indicator-table th,
        .indicator-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
        }

        .indicator-table th {
            background: #f5f5f5;
            font-weight: 500;
            color: var(--text-primary);
        }

        .indicator-table td {
            color: var(--text-primary);
        }

        .indicator-value {
            font-weight: 600;
        }

        .indicator-value.high {
            color: var(--error-red);
        }

        .indicator-value.medium {
            color: var(--warning-orange);
        }

        .indicator-value.low {
            color: var(--success-green);
        }

        .evaluation-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
        }

        .evaluation-summary h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-number {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .summary-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-warning {
            background: var(--warning-orange);
            border-color: var(--warning-orange);
            color: #ffffff;
        }

        .btn-warning:hover {
            background: #d46b08;
            border-color: #d46b08;
        }

        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: #ffffff;
        }

        .btn-success:hover {
            background: #389e0d;
            border-color: #389e0d;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }

        .risk-explanation {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
        }

        .risk-explanation h4 {
            margin: 0 0 12px 0;
            color: var(--success-green);
            font-size: 16px;
            font-weight: 500;
        }

        .risk-explanation p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        .transaction-chart {
            background: var(--bg-container);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-placeholder {
            height: 300px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* 360度风险画像样式 */
        .risk-radar {
            position: relative;
            width: 100%;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 40px 0;
        }

        .risk-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .merchant-avatar {
            width: 80px;
            height: 80px;
            background: var(--primary-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
        }

        .merchant-avatar i {
            font-size: 32px;
            color: white;
        }

        .merchant-info {
            background: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .merchant-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .merchant-id {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        /* 风险指标卡片 */
        .risk-card {
            position: absolute;
            width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color-light);
            transition: all 0.3s ease;
        }

        .risk-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .risk-card-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-card-header i {
            font-size: 16px;
            color: var(--primary-blue);
        }

        .risk-card-header span:nth-child(2) {
            flex: 1;
            font-weight: 500;
            color: var(--text-primary);
        }

        .risk-card-content {
            padding: 16px;
        }

        .risk-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 1.4;
        }

        .risk-item:last-child {
            margin-bottom: 0;
        }

        .risk-label {
            color: var(--text-secondary);
            min-width: 70px;
            margin-right: 8px;
        }

        .risk-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .risk-tag.risk-high {
            background: #fff2f0;
            color: var(--error-red);
        }

        .risk-tag.risk-medium {
            background: #fff7e6;
            color: var(--warning-orange);
        }

        .risk-tag.risk-low {
            background: #f6ffed;
            color: var(--success-green);
        }

        /* 风险卡片位置 - 只显示中高风险 */
        .risk-card-2 { top: 10%; left: 10%; }
        .risk-card-3 { top: 10%; right: 10%; }
        .risk-card-5 { bottom: 10%; left: 50%; transform: translateX(-50%); }

        /* 低风险指标底部排列 */
        .low-risk-section {
            margin-top: 40px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }

        .low-risk-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .low-risk-card {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">存量商户风险详情</h1>
            <div style="display: flex; gap: 12px; align-items: center;">
                <button class="btn btn-success" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    导出评估报告
                </button>
                <a href="javascript:history.back()" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <!-- 评估概要 -->
        <div class="evaluation-summary">
            <h3><i class="fas fa-chart-line"></i> 风险评估概要</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number">中风险</div>
                    <div class="summary-label">综合风险等级</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">2024-01-01</div>
                    <div class="summary-label">评估时间</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">6个月</div>
                    <div class="summary-label">经营时长</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">85分</div>
                    <div class="summary-label">风险评分</div>
                </div>
            </div>
        </div>

        <!-- 基础信息 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">基础信息</h3>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">商户号</div>
                        <div class="info-value">M202301002</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">商户名称</div>
                        <div class="info-value">小李超市</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">负责人</div>
                        <div class="info-value">李明</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">联系电话</div>
                        <div class="info-value">138****5678</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">市级分公司</div>
                        <div class="info-value">北京分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">区县分公司</div>
                        <div class="info-value">海淀区分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">营业所</div>
                        <div class="info-value">建国门营业所</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">进件时间</div>
                        <div class="info-value">2023-07-15</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">当前风险等级</div>
                        <div class="info-value">
                            <span class="risk-tag risk-medium">中风险</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 360度风险画像 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">商户风险画像</h3>
                <span class="text-sm text-gray-500">基于交易行为的360度风险评估</span>
            </div>
            <div class="card-body">
                <div class="risk-radar">
                    <!-- 中心商户图标 -->
                    <div class="risk-center">
                        <div class="merchant-avatar">
                            <i class="fas fa-store"></i>
                        </div>
                        <div class="merchant-info">
                            <div class="merchant-name">小李超市</div>
                            <div class="merchant-id">M202301002</div>
                            <div class="risk-level risk-medium">中风险</div>
                        </div>
                    </div>

                    <!-- 存量商户风险指标卡片 -->


                    <div class="risk-card risk-card-2">
                        <div class="risk-card-header">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>大额交易</span>
                            <span class="risk-tag risk-medium">中风险</span>
                        </div>
                        <div class="risk-card-content">
                            <div class="risk-item">
                                <span class="risk-label">判定标准：</span>
                                <span>单笔交易≥2000元</span>
                            </div>
                            <div class="risk-item">
                                <span class="risk-label">当前情况：</span>
                                <span>月均大额交易5笔，最大3500元</span>
                            </div>
                        </div>
                    </div>

                    <div class="risk-card risk-card-3">
                        <div class="risk-card-header">
                            <i class="fas fa-network-wired"></i>
                            <span>IP异常交易</span>
                            <span class="risk-tag risk-medium">中风险</span>
                        </div>
                        <div class="risk-card-content">
                            <div class="risk-item">
                                <span class="risk-label">判定标准：</span>
                                <span>异地IP交易2-9笔</span>
                            </div>
                            <div class="risk-item">
                                <span class="risk-label">当前情况：</span>
                                <span>检测到异常退款率8%</span>
                            </div>
                        </div>
                    </div>



                    <div class="risk-card risk-card-5">
                        <div class="risk-card-header">
                            <i class="fas fa-credit-card"></i>
                            <span>信用卡交易占比</span>
                            <span class="risk-tag risk-medium">中风险</span>
                        </div>
                        <div class="risk-card-content">
                            <div class="risk-item">
                                <span class="risk-label">判定标准：</span>
                                <span>信用卡交易占比50%-80%</span>
                            </div>
                            <div class="risk-item">
                                <span class="risk-label">当前情况：</span>
                                <span>信用卡交易占比65%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 低风险指标底部排列 -->
                <div class="low-risk-section">
                    <h4 style="margin: 0 0 16px 0; color: var(--text-primary);">低风险指标</h4>
                    <div class="low-risk-grid">
                        <!-- 交易时间 -->
                        <div class="risk-card" style="position: static; width: auto; margin-bottom: 16px;">
                            <div class="risk-card-header">
                                <i class="fas fa-clock"></i>
                                <span>交易时间</span>
                                <span class="risk-tag risk-low">低风险</span>
                            </div>
                            <div class="risk-card-content">
                                <div class="risk-item">
                                    <span class="risk-label">判定标准：</span>
                                    <span>集中在3小时外交易</span>
                                </div>
                                <div class="risk-item">
                                    <span class="risk-label">当前情况：</span>
                                    <span>交易时间分布正常，8:00-22:00</span>
                                </div>
                            </div>
                        </div>

                        <!-- 交易人群 -->
                        <div class="risk-card" style="position: static; width: auto; margin-bottom: 16px;">
                            <div class="risk-card-header">
                                <i class="fas fa-users"></i>
                                <span>交易人群</span>
                                <span class="risk-tag risk-low">低风险</span>
                            </div>
                            <div class="risk-card-content">
                                <div class="risk-item">
                                    <span class="risk-label">判定标准：</span>
                                    <span>重复消费者占比<20%</span>
                                </div>
                                <div class="risk-item">
                                    <span class="risk-label">当前情况：</span>
                                    <span>重复消费者占比45%，新客户25%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 设备使用 -->
                        <div class="risk-card" style="position: static; width: auto; margin-bottom: 16px;">
                            <div class="risk-card-header">
                                <i class="fas fa-mobile-alt"></i>
                                <span>设备使用</span>
                                <span class="risk-tag risk-low">低风险</span>
                            </div>
                            <div class="risk-card-content">
                                <div class="risk-item">
                                    <span class="risk-label">判定标准：</span>
                                    <span>设备使用正常</span>
                                </div>
                                <div class="risk-item">
                                    <span class="risk-label">当前情况：</span>
                                    <span>设备使用正常，异常使用占比2%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 优惠券使用 -->
                        <div class="risk-card" style="position: static; width: auto; margin-bottom: 16px;">
                            <div class="risk-card-header">
                                <i class="fas fa-percentage"></i>
                                <span>优惠券使用</span>
                                <span class="risk-tag risk-low">低风险</span>
                            </div>
                            <div class="risk-card-content">
                                <div class="risk-item">
                                    <span class="risk-label">判定标准：</span>
                                    <span>优惠券使用占比正常</span>
                                </div>
                                <div class="risk-item">
                                    <span class="risk-label">当前情况：</span>
                                    <span>优惠券使用占比35%，正常范围</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>




            </div>
        </div>

        <!-- 返回按钮 -->
        <div style="position: fixed; bottom: 24px; right: 24px;">
            <a href="existing-merchant-risk.html" class="btn btn-primary" style="border-radius: 50%; width: 56px; height: 56px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);">
                <i class="fas fa-arrow-left" style="font-size: 18px;"></i>
            </a>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 页面加载时获取商户ID
        document.addEventListener('DOMContentLoaded', function() {
            const merchantId = getUrlParameter('id');
            if (merchantId) {
                console.log('商户ID:', merchantId);
                // 这里可以根据商户ID加载具体数据
                loadMerchantData(merchantId);
            }
        });

        // 加载商户数据
        function loadMerchantData(merchantId) {
            // 模拟数据加载
            console.log('加载商户数据:', merchantId);
            // 这里可以调用API获取商户详细信息
        }

        // 下发整改任务
        function issueRectificationTask() {
            const merchantId = getUrlParameter('id') || 'M202301002';
            if (confirm('确定要为该商户下发整改任务吗？')) {
                alert(`正在为商户 ${merchantId} 下发整改任务...`);
                // 这里可以调用API下发整改任务
            }
        }

        // 下发走访任务
        function issueVisitTask() {
            const merchantId = getUrlParameter('id') || 'M202301002';
            if (confirm('确定要为该商户下发走访任务吗？')) {
                alert(`正在为商户 ${merchantId} 下发走访任务...`);
                // 这里可以调用API下发走访任务
            }
        }

        // 导出评估报告
        function exportReport() {
            const merchantId = getUrlParameter('id') || 'M202301002';
            alert(`正在导出商户 ${merchantId} 的风险评估报告...`);
            // 这里可以调用API导出报告
        }

        // 添加风险卡片悬停效果
        document.addEventListener('DOMContentLoaded', function() {
            const riskCards = document.querySelectorAll('.risk-card');
            riskCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>